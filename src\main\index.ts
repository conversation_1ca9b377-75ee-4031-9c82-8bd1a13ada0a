import { app, shell, BrowserWindow, ipcMain, dialog } from 'electron'
import { join } from 'path'
// import { electronApp, optimizer, is } from '@electron-toolkit/utils'
// import icon from '../../resources/icon.png?asset'
import { WindowsSystemAPI } from '../native/windows-api'

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 1000,
    minHeight: 700,
    show: false,
    autoHideMenuBar: true,
    // ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      contextIsolation: true,
      enableRemoteModule: false,
      nodeIntegration: false
    },
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: '#1e1e1e',
      symbolColor: '#ffffff'
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (process.env.NODE_ENV === 'development' && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  app.setAppUserModelId('com.sulindvaas.winoptimizer')

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// IPC handlers for system operations
const systemAPI = new WindowsSystemAPI()

ipcMain.handle('system:cleanCache', async () => {
  try {
    return await systemAPI.cleanSystemCache()
  } catch (error) {
    console.error('Cache cleaning failed:', error)
    throw error
  }
})

ipcMain.handle('system:cleanTempFiles', async () => {
  try {
    return await systemAPI.cleanTempFiles()
  } catch (error) {
    console.error('Temp files cleaning failed:', error)
    throw error
  }
})

ipcMain.handle('system:getInstalledGames', async () => {
  try {
    return await systemAPI.getInstalledGames()
  } catch (error) {
    console.error('Failed to get installed games:', error)
    throw error
  }
})

ipcMain.handle('system:launchGame', async (_, gamePath: string) => {
  try {
    return await systemAPI.launchGame(gamePath)
  } catch (error) {
    console.error('Failed to launch game:', error)
    throw error
  }
})

ipcMain.handle('system:getSystemHealth', async () => {
  try {
    return await systemAPI.getSystemHealth()
  } catch (error) {
    console.error('Failed to get system health:', error)
    throw error
  }
})

ipcMain.handle('system:optimizeWindows', async (_, options: any) => {
  try {
    return await systemAPI.optimizeWindows(options)
  } catch (error) {
    console.error('Windows optimization failed:', error)
    throw error
  }
})

ipcMain.handle('dialog:showMessageBox', async (_, options) => {
  const result = await dialog.showMessageBox(options)
  return result
})

// In this file you can include the rest of your app"s main process code.
// You can also put them in separate files and require them here.
