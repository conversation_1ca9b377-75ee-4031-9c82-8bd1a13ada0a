import { WindowsSystemAPI } from '../../src/native/windows-api'

describe('WindowsSystemAPI', () => {
  let api: WindowsSystemAPI

  beforeEach(() => {
    api = new WindowsSystemAPI()
  })

  describe('cleanSystemCache', () => {
    it('should return cleanup result with success status', async () => {
      const result = await api.cleanSystemCache()
      
      expect(result).toHaveProperty('success')
      expect(result).toHaveProperty('freedSpace')
      expect(result).toHaveProperty('errors')
      expect(result).toHaveProperty('details')
      expect(typeof result.success).toBe('boolean')
      expect(typeof result.freedSpace).toBe('number')
      expect(Array.isArray(result.errors)).toBe(true)
      expect(Array.isArray(result.details)).toBe(true)
    })

    it('should handle errors gracefully', async () => {
      // Mock a scenario where cleanup fails
      const result = await api.cleanSystemCache()
      
      if (!result.success) {
        expect(result.errors.length).toBeGreaterThan(0)
      }
    })
  })

  describe('cleanTempFiles', () => {
    it('should clean temporary files successfully', async () => {
      const result = await api.cleanTempFiles()
      
      expect(result).toHaveProperty('success')
      expect(result).toHaveProperty('freedSpace')
      expect(typeof result.freedSpace).toBe('number')
      expect(result.freedSpace).toBeGreaterThanOrEqual(0)
    })
  })

  describe('getInstalledGames', () => {
    it('should return array of games', async () => {
      const games = await api.getInstalledGames()
      
      expect(Array.isArray(games)).toBe(true)
      
      if (games.length > 0) {
        const game = games[0]
        expect(game).toHaveProperty('name')
        expect(game).toHaveProperty('path')
        expect(typeof game.name).toBe('string')
        expect(typeof game.path).toBe('string')
      }
    })
  })

  describe('getSystemHealth', () => {
    it('should return system health information', async () => {
      const health = await api.getSystemHealth()
      
      expect(health).toHaveProperty('diskHealth')
      expect(health).toHaveProperty('memoryUsage')
      expect(health).toHaveProperty('cpuUsage')
      expect(health).toHaveProperty('systemErrors')
      
      expect(Array.isArray(health.diskHealth)).toBe(true)
      expect(typeof health.memoryUsage).toBe('object')
      expect(typeof health.cpuUsage).toBe('number')
      expect(Array.isArray(health.systemErrors)).toBe(true)
    })

    it('should return valid memory usage data', async () => {
      const health = await api.getSystemHealth()

      expect(health.memoryUsage).toHaveProperty('total')
      expect(health.memoryUsage).toHaveProperty('used')
      expect(health.memoryUsage).toHaveProperty('available')

      expect(health.memoryUsage.total).toBeGreaterThanOrEqual(0)
      expect(health.memoryUsage.used).toBeGreaterThanOrEqual(0)
      expect(health.memoryUsage.available).toBeGreaterThanOrEqual(0)
    })
  })

  describe('launchGame', () => {
    it('should handle invalid game paths', async () => {
      // Mock spawn to avoid actual process creation
      const originalSpawn = require('child_process').spawn
      require('child_process').spawn = jest.fn().mockImplementation(() => {
        throw new Error('ENOENT')
      })

      const result = await api.launchGame('invalid/path/game.exe')
      expect(typeof result).toBe('boolean')
      expect(result).toBe(false)

      // Restore original spawn
      require('child_process').spawn = originalSpawn
    })
  })

  describe('optimizeWindows', () => {
    it('should return optimization result', async () => {
      const options = {
        disableStartupPrograms: true,
        optimizeVisualEffects: false
      }
      
      const result = await api.optimizeWindows(options)
      
      expect(result).toHaveProperty('success')
      expect(result).toHaveProperty('freedSpace')
      expect(result).toHaveProperty('errors')
      expect(result).toHaveProperty('details')
    })
  })
})
